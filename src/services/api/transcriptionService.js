import axiosInstance from "./axiosInstance";
import {
  GET_TRANSCRIPTIONS,
  GET_TRANSCRIPTION_BY_ID,
  EXPORT_TRANSCRIPTION,
  BATCH_EXPORT_TRANSCRIPTIONS,
  EXPORT_OUTLINE,
  CREATE_TRANSCRIPTION_TASK,
  CREATE_YOU<PERSON>BE_TRANSCRIPTION_TASK,
  RENAME_TRANSCRIPTION,
  DELETE_TRANSCRIPTION,
  DELETE_ORIGINAL_FILE,
  BATCH_DELETE_TRANSCRIPTIONS,
  BATCH_MOVE_TRANSCRIPTIONS,
  GET_TRANSCRIPTIONS_BY_PAGE,
  SEARCH_TRANSCRIPTIONS,
  UPDATE_LANGUAGE_CODE,
  GET_ANONYMOUS_LATEST_FILE,
  MIGRATE_ANONYMOUS_FILE,
  UPDATE_TRANSCRIPTION_TYPE,
  UPDATE_SPEAKER_RECOGNITION,
  UNLOCK_TRANSCRIPTION,
  UPDATE_SEGMENT,
  BATCH_UPDATE_SEGMENTS,
  BATCH_UPDATE_SPEAKERS,
  MOVE_TRANSCRIPTION_TO_FOLDER,
  RETRY_TASK,
  GET_TASK_STATUS,
} from "@/constants/endpoints";

export const transcriptionService = {
  getTranscriptions: async (cursor = null) => {
    const params = cursor ? { cursor } : {};
    return await axiosInstance.get(GET_TRANSCRIPTIONS, { params });
  },

  getTranscriptionById: async (id) => {
    const response = await axiosInstance.get(GET_TRANSCRIPTION_BY_ID(id));
    return response;
  },

  exportTranscription: async (fileId, format, options = {}) => {
    const response = await axiosInstance.post(
      EXPORT_TRANSCRIPTION,
      {
        fileId,
        fileType: format,
        ...(typeof options.showSpeaker === "boolean"
          ? { showSpeakerName: options.showSpeaker }
          : {}),
        ...(typeof options.showTimestamp === "boolean"
          ? { showTimestamps: options.showTimestamp }
          : {}),
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
        responseType: "blob", // 设置响应类型为blob
      }
    );
    return response;
  },

  exportOutline: async (transcriptionId, format = "md") => {
    const response = await axiosInstance.post(
      EXPORT_OUTLINE,
      {
        transcriptionId,
        format,
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
        responseType: "blob", // 设置响应类型为blob
      }
    );
    return response;
  },

  /**
   * 批量导出转录记录
   * @param {string[]} fileIds - 要导出的转录文件ID数组（字符串格式）
   * @param {string} fileType - 导出文件格式
   * @param {Object} options - 导出选项
   * @returns {Promise} API响应
   */
  batchExportTranscriptions: async (fileIds, fileType, options = {}) => {
    const requestData = {
      fileIds: fileIds,
      fileType: fileType,
      ...(typeof options.showSpeaker === "boolean"
        ? { showSpeakerName: options.showSpeaker }
        : {}),
      ...(typeof options.showTimestamp === "boolean"
        ? { showTimestamps: options.showTimestamp }
        : {}),
    };

    const response = await axiosInstance.post(
      BATCH_EXPORT_TRANSCRIPTIONS,
      requestData,
      {
        headers: {
          "Content-Type": "application/json",
        },
        responseType: "blob", // 设置响应类型为blob
      }
    );
    return response;
  },

  doTranscription: async (transcriptionFileId) => {
    const response = await axiosInstance.post(CREATE_TRANSCRIPTION_TASK, {
      transcriptionFileId,
    });
    return response;
  },

  createYoutubeTranscriptionTask: async (
    url,
    title,
    duration,
    subtitleEnabled,
    languageCode,
    enableSpeakerDiarization = false,
    folderId = null
  ) => {
    const transcriptionType = subtitleEnabled ? "subtitle" : "transcript";

    const requestData = {
      url,
      title,
      duration,
      transcriptionType,
      languageCode,
      enableSpeakerDiarization,
    };

    // 只有当folderId不为null且不为"all"时才添加到请求中
    if (folderId && folderId !== "all") {
      requestData.folderId = String(folderId);
    }

    const response = await axiosInstance.post(
      CREATE_YOUTUBE_TRANSCRIPTION_TASK,
      requestData
    );
    return response;
  },

  renameTranscription: async (id, filename) => {
    const response = await axiosInstance.patch(RENAME_TRANSCRIPTION(id), {
      filename,
    });
    return response;
  },

  updateLanguageCode: async (id, languageCode) => {
    const response = await axiosInstance.patch(UPDATE_LANGUAGE_CODE(id), {
      languageCode,
    });
    return response;
  },

  deleteTranscription: async (id) => {
    const response = await axiosInstance.delete(DELETE_TRANSCRIPTION(id));
    return response;
  },

  deleteOriginalFile: async (id) => {
    const response = await axiosInstance.delete(DELETE_ORIGINAL_FILE(id));
    return response;
  },

  /**
   * 批量删除转录记录
   * @param {string[]} transcriptionIds - 要删除的转录记录ID数组（推荐使用字符串格式避免大整数精度丢失）
   * @returns {Promise} API响应
   */
  batchDeleteTranscriptions: async (transcriptionIds) => {
    // 根据API规范使用transcriptionIds字段，支持字符串格式的大整数ID
    const requestData = {
      transcriptionIds: transcriptionIds,
    };

    const response = await axiosInstance.delete(BATCH_DELETE_TRANSCRIPTIONS, {
      data: requestData,
    });
    return response;
  },

  // 新增基于页码的分页接口
  getTranscriptionsByPage: async (page, pageSize, folder = null) => {
    const params = {
      page,
      pageSize,
    };

    // 如果指定了folder参数，添加到请求中
    if (folder) {
      params.folder = folder;
    }

    return await axiosInstance.get(GET_TRANSCRIPTIONS_BY_PAGE, {
      params,
    });
  },

  searchTranscriptions: (keyword, page, pageSize, folderId = null) => {
    const params = {
      keyword,
      page,
      pageSize,
    };

    // 只有当folderId不为null且不为"all"时才添加到请求中
    if (folderId && folderId !== "all") {
      params.folderId = folderId;
    }

    return axiosInstance.get(SEARCH_TRANSCRIPTIONS, {
      params,
    });
  },

  getAnonymousLatestFile: async () => {
    const response = await axiosInstance.get(GET_ANONYMOUS_LATEST_FILE);
    return response;
  },

  migrateAnonymousFile: async (fileId) => {
    const response = await axiosInstance.post(MIGRATE_ANONYMOUS_FILE, {
      fileId,
    });
    return response;
  },

  updateTranscriptionType: async (id, transcriptionType) => {
    const response = await axiosInstance.patch(UPDATE_TRANSCRIPTION_TYPE(id), {
      transcriptionType,
    });
    return response;
  },

  updateSpeakerRecognition: async (id, enableSpeakerDiarization) => {
    const response = await axiosInstance.patch(UPDATE_SPEAKER_RECOGNITION(id), {
      enableSpeakerDiarization,
    });
    return response;
  },

  unlockTranscription: async (id) => {
    const response = await axiosInstance.post(UNLOCK_TRANSCRIPTION(id));
    return response;
  },

  updateSegment: async (fileId, segmentId, text) => {
    const response = await axiosInstance.patch(
      UPDATE_SEGMENT(fileId, segmentId),
      {
        text,
      }
    );
    return response;
  },

  batchUpdateSegments: async (fileId, segments) => {
    const response = await axiosInstance.patch(BATCH_UPDATE_SEGMENTS(fileId), {
      segments,
    });
    return response;
  },

  batchUpdateSpeakers: async (fileId, speakers) => {
    const response = await axiosInstance.patch(BATCH_UPDATE_SPEAKERS(fileId), {
      speakers,
    });
    return response;
  },
  /**
   * 批量移动转录记录到文件夹
   * @param {string[]} transcriptionIds - 转录记录ID数组（推荐使用字符串格式避免大整数精度丢失）
   * @param {string|null} folderId - 文件夹ID，null表示移动到未分类状态
   * @returns {Promise} API响应
   */
  batchMoveToFolder: async (transcriptionIds, folderId) => {
    const requestData = {
      transcriptionIds: transcriptionIds,
      folderId: folderId,
    };

    const response = await axiosInstance.patch(
      BATCH_MOVE_TRANSCRIPTIONS,
      requestData
    );
    return response;
  },

  /**
   * 移动转录记录到文件夹（单个）- 现在使用批量API实现
   * @param {string} transcriptionId - 转录记录ID
   * @param {string|null} folderId - 文件夹ID，null表示移动到未分类状态
   * @returns {Promise} API响应
   */
  moveToFolder: async (transcriptionId, folderId) => {
    // 使用批量移动API来实现单个移动，保持向后兼容
    return await transcriptionService.batchMoveToFolder(
      [transcriptionId],
      folderId
    );
  },

  /**
   * 重试失败的任务
   * @param {number} taskId - 任务ID
   * @returns {Promise} API响应
   */
  retryTask: async (taskId) => {
    const response = await axiosInstance.post(RETRY_TASK, {
      taskId,
    });
    return response;
  },

  /**
   * 查询单个任务状态
   * @param {number} taskId - 任务ID
   * @returns {Promise} API响应
   */
  getTaskStatus: async (taskId) => {
    const response = await axiosInstance.get(GET_TASK_STATUS(taskId));
    return response;
  },
};
