"use client";

import React, { useState, useCallback, useEffect } from "react";
import { useMediaQuery } from "@/hooks/useMediaQuery";
import { useSearchParams } from "next/navigation";
import { TranscriptionProvider } from "@/contexts/TranscriptionContext";
import DashboardV2 from "@/components/Dashboard/DashboardV2";
import DashboardMobile from "@/components/Dashboard/DashboardMobile";
import DashboardLayout from "@/components/Dashboard/DashboardLayout";

export default function Page() {
  const isMobile = useMediaQuery("(max-width: 768px)");
  const searchParams = useSearchParams();
  const [selectedFolderId, setSelectedFolderId] = useState("all");

  // 从URL参数初始化selectedFolderId
  useEffect(() => {
    const folderParam = searchParams.get("folder");
    if (folderParam) {
      setSelectedFolderId(folderParam);
    } else {
      // 没有folder参数时，表示选中Home，传递"home"给子组件
      setSelectedFolderId("home");
    }
  }, [searchParams]);

  const handleFolderChange = useCallback((folderId) => {
    setSelectedFolderId(folderId);
  }, []);

  return (
    <TranscriptionProvider>
      {isMobile ? (
        <DashboardMobile />
      ) : (
        <DashboardLayout onFolderChange={handleFolderChange}>
          <DashboardV2 selectedFolderId={selectedFolderId} />
        </DashboardLayout>
      )}
    </TranscriptionProvider>
  );
}
